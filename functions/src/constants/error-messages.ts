/**
 * Error message keys and parameter definitions for internationalization
 * These keys will be used in the UI to display localized error messages
 */

export interface ErrorMessageParams {
  [key: string]: string | number;
}

export interface ErrorResponse {
  errorKey: string;
  params?: ErrorMessageParams;
  fallbackMessage?: string;
}

// Authentication errors
export const AUTH_ERRORS = {
  UNAUTHENTICATED: "errors.auth.unauthenticated",
  PERMISSION_DENIED: "errors.auth.permissionDenied",
  PERMISSION_DENIED_WITH_OPERATION: "errors.auth.permissionDeniedWithOperation",
  ADMIN_ONLY: "errors.auth.adminOnly",
  USER_NOT_FOUND: "errors.auth.userNotFound",
  TON_WALLET_REQUIRED: "errors.auth.tonWalletRequired",
  WALLET_ALREADY_USED: "errors.auth.walletAlreadyUsed",
} as const;

// Validation errors
export const VALIDATION_ERRORS = {
  REQUIRED_FIELD: "errors.validation.requiredField",
  POSITIVE_AMOUNT_REQUIRED: "errors.validation.positiveAmountRequired",
  INVALID_ORDER_ID: "errors.validation.invalidOrderId",
  INVALID_COLLECTION_ID: "errors.validation.invalidCollectionId",
  INVALID_PRICE: "errors.validation.invalidPrice",
  INVALID_SECONDARY_MARKET_PRICE:
    "errors.validation.invalidSecondaryMarketPrice",
  INVALID_BOT_TOKEN: "errors.validation.invalidBotToken",
  BOT_TOKEN_REQUIRED: "errors.validation.botTokenRequired",
  OWNED_GIFT_ID_REQUIRED: "errors.validation.ownedGiftIdRequired",
  USER_ID_OR_TG_ID_REQUIRED: "errors.validation.userIdOrTgIdRequired",
} as const;

// Order errors
export const ORDER_ERRORS = {
  ORDER_NOT_FOUND: "errors.order.orderNotFound",
  INSUFFICIENT_BALANCE: "errors.order.insufficientBalance",
  COLLECTION_NOT_FOUND: "errors.order.collectionNotFound",
  COLLECTION_NOT_ACTIVE: "errors.order.collectionNotActive",
  ONLY_PAID_ORDERS_SECONDARY_MARKET:
    "errors.order.onlyPaidOrdersSecondaryMarket",
  ONLY_BUYER_CAN_SET_SECONDARY_PRICE:
    "errors.order.onlyBuyerCanSetSecondaryPrice",
  ORDER_MUST_HAVE_BUYER_AND_SELLER: "errors.order.orderMustHaveBuyerAndSeller",
  SECONDARY_PRICE_EXCEEDS_COLLATERAL:
    "errors.order.secondaryPriceExceedsCollateral",
  ORDER_NOT_AVAILABLE_SECONDARY_MARKET:
    "errors.order.orderNotAvailableSecondaryMarket",
  ONLY_PAID_ORDERS_PURCHASABLE: "errors.order.onlyPaidOrdersPurchasable",
  SELLER_CANNOT_PURCHASE_OWN_ORDER: "errors.order.sellerCannotPurchaseOwnOrder",
  BUYER_CANNOT_PURCHASE_SAME_ORDER: "errors.order.buyerCannotPurchaseSameOrder",
  SECONDARY_PRICE_BELOW_MINIMUM: "errors.order.secondaryPriceBelowMinimum",
  ORDER_MUST_BE_PAID_STATUS: "errors.order.orderMustBePaidStatus",
  ORDER_MUST_BE_GIFT_SENT_STATUS: "errors.order.orderMustBeGiftSentStatus",
} as const;

// Withdrawal errors
export const WITHDRAWAL_ERRORS = {
  AMOUNT_BELOW_MINIMUM: "errors.withdrawal.amountBelowMinimum",
  AMOUNT_ABOVE_MAXIMUM: "errors.withdrawal.amountAboveMaximum",
  AMOUNT_EXCEEDS_24H_LIMIT: "errors.withdrawal.amountExceeds24hLimit",
  INSUFFICIENT_AVAILABLE_BALANCE:
    "errors.withdrawal.insufficientAvailableBalance",
  AMOUNT_TOO_SMALL_AFTER_FEES: "errors.withdrawal.amountTooSmallAfterFees",
} as const;

// Telegram auth errors
export const TELEGRAM_ERRORS = {
  INIT_DATA_REQUIRED: "errors.telegram.initDataRequired",
  BOT_TOKEN_NOT_CONFIGURED: "errors.telegram.botTokenNotConfigured",
  INVALID_TELEGRAM_DATA: "errors.telegram.invalidTelegramData",
  FIREBASE_AUTH_ERROR: "errors.telegram.firebaseAuthError",
  IAM_PERMISSION_ERROR: "errors.telegram.iamPermissionError",
} as const;

// Generic errors
export const GENERIC_ERRORS = {
  SERVER_ERROR: "errors.generic.serverError",
  UNKNOWN_ERROR: "errors.generic.unknownError",
  OPERATION_FAILED: "errors.generic.operationFailed",
  AUTHENTICATION_FAILED: "errors.generic.authenticationFailed",
} as const;

// Helper function to create error response
export function createErrorResponse(
  errorKey: string,
  params?: ErrorMessageParams,
  fallbackMessage?: string
): ErrorResponse {
  return {
    errorKey,
    params,
    fallbackMessage,
  };
}

// Helper function to create HttpsError with internationalization data
export function createInternationalizedError(
  code: string,
  errorKey: string,
  params?: ErrorMessageParams,
  fallbackMessage?: string
): Error {
  const errorResponse = createErrorResponse(errorKey, params, fallbackMessage);
  const error = new Error(JSON.stringify(errorResponse));
  (error as any).code = code;
  return error;
}
