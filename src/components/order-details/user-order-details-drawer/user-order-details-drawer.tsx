'use client';

import { openTelegramLink, shareURL } from '@telegram-apps/sdk-react';
import { Share } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';

import { orderDetailsContentMessages } from '@/components/order-details/intl/order-details-content.messages';
import { OrderDetailsDate } from '@/components/order-details/order-details-date';
import { OrderDetailsFeesSection } from '@/components/order-details/order-details-fees-section';
import { OrderStatusBadge } from '@/components/shared/order-status-badge';
import { SellPriceDetails } from '@/components/shared/sell-price-details';
import { Button } from '@/components/ui/button';
import { ResellTxHistory } from '@/components/ui/order/resell-tx-history';
import { OrderActors } from '@/components/ui/order-actors';
import type { OrderEntity, UserType } from '@/core.constants';
import { OrderStatus } from '@/core.constants';
import { useOrderTimers } from '@/hooks/use-order-timers';
import { useRootContext } from '@/root-context';
import { shouldShowSellerEarnings } from '@/services/order-service';
import { generateOrderShareLink } from '@/utils/order-deep-link-utils';

import { ResellOrderPriceDrawer } from '../../../app/(app)/marketplace/resell/resell-order-price-drawer';
import { CancelOrderDrawer } from '../../../app/(app)/orders/cancel-order-drawer';
import { OrderDetailsBaseDrawer } from '../order-details-drawer/order-details-base-drawer';
import {
  UserOrderActionsSection,
  UserOrderDeadlineSection,
  UserOrderImageSection,
  UserOrderSellerEarningsSection,
  UserOrderStatusAlerts,
} from '.';

interface UserOrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  userType: UserType;
  onOrderUpdate: () => void;
}

export function UserOrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  userType,
  onOrderUpdate,
}: UserOrderDetailsDrawerProps) {
  const { formatMessage: t } = useIntl();
  const { collections, currentUser } = useRootContext();
  const [showCancelDrawer, setShowCancelDrawer] = useState(false);
  const [showResellPriceDrawer, setShowResellPriceDrawer] = useState(false);
  const [showResellHistory, setShowResellHistory] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  const collection =
    collections.find((c) => c.id === order?.collectionId) || null;
  const { timeLeft, isFreezed } = useOrderTimers({ order, collection });

  const handleCancelOrder = () => setShowCancelDrawer(true);
  const handleCreateSecondaryMarketOrder = () => setShowResellPriceDrawer(true);
  // TODO: Temporarily hidden - resell tx history feature
  const handleShowResellHistory = () => {
    // setShowResellHistory(true);
  };

  const handleOrderCancelled = () => {
    onOrderUpdate();
    onOpenChange(false);
  };

  const handleOrderResold = () => {
    onOrderUpdate();
    setShowResellPriceDrawer(false);
    onOpenChange(false);
  };

  const handleShare = async () => {
    setIsSharing(true);

    if (!order?.id) {
      toast.error('Order ID not available');
      setIsSharing(false);
      return;
    }

    try {
      const shareLink = generateOrderShareLink(order.id);

      if (shareURL.isAvailable()) {
        shareURL(shareLink, 'Check out this order!');
      } else if (openTelegramLink.isAvailable()) {
        openTelegramLink(shareLink);
      } else {
        window.open(shareLink, '_blank');
      }
    } catch (error) {
      console.error('Error sharing order:', error);
      toast.error('Failed to share order');
    } finally {
      setIsSharing(false);
    }
  };

  if (!order) return null;

  return (
    <>
      <OrderDetailsBaseDrawer open={open} onOpenChange={onOpenChange}>
        <div className="flex justify-center gap-3 items-center">
          <h2 className="text-xl font-bold text-[#f5f5f5]">
            Order #{order.number}
          </h2>
          <OrderStatusBadge order={order} />
        </div>

        <div className="w-[50%] mx-auto">
          <UserOrderImageSection collection={collection} />
        </div>

        <div className="flex justify-center">
          <Button
            onClick={handleShare}
            disabled={isSharing}
            variant="outline"
            className="border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#232e3c]/50 bg-transparent rounded-2xl"
          >
            {isSharing ? (
              <>
                <Share className="w-4 h-4 mr-2 animate-pulse" />
                {t(orderDetailsContentMessages.openingTelegram)}
              </>
            ) : (
              <>
                <Share className="w-4 h-4 mr-1" />
                {t(orderDetailsContentMessages.share)}
              </>
            )}
          </Button>
        </div>

        <SellPriceDetails order={order} className="py-4" />

        <OrderDetailsFeesSection order={order} />

        <OrderDetailsDate updatedAt={order.updatedAt} />

        {order.status === OrderStatus.PAID && (
          <div className="space-y-4">
            <UserOrderDeadlineSection
              {...{
                order,
                userType,
                timeLeft,
              }}
            />
            <UserOrderStatusAlerts
              {...{
                order,
                userType,
                isFreezed,
              }}
            />
          </div>
        )}

        {shouldShowSellerEarnings(currentUser, order) && (
          <UserOrderSellerEarningsSection order={order} />
        )}

        {order.status !== OrderStatus.ACTIVE && (
          <OrderActors
            buyerId={order.buyerId}
            sellerId={order.sellerId}
            isOpen={open}
          />
        )}

        <UserOrderActionsSection
          order={order}
          currentUserId={currentUser?.id}
          onCancelOrder={handleCancelOrder}
          onCreateSecondaryMarketOrder={handleCreateSecondaryMarketOrder}
          onShowResellHistory={handleShowResellHistory}
        />
      </OrderDetailsBaseDrawer>

      <CancelOrderDrawer
        open={showCancelDrawer}
        onOpenChange={setShowCancelDrawer}
        order={order}
        onOrderCancelled={handleOrderCancelled}
      />

      <ResellOrderPriceDrawer
        open={showResellPriceDrawer}
        onOpenChange={setShowResellPriceDrawer}
        order={order}
        onOrderResold={handleOrderResold}
      />

      {showResellHistory && order && (
        <div className="fixed inset-0 bg-black/50 z-[60] flex items-center justify-center p-4">
          <div className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <ResellTxHistory
              order={order}
              onClose={() => setShowResellHistory(false)}
            />
          </div>
        </div>
      )}
    </>
  );
}
