'use client';

import { Eye, Loader2 } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import { useIntl } from 'react-intl';

import { OrderDetailsDrawer } from '@/components/order-details/order-details-drawer/order-details-drawer';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';
import type { CollectionEntity, OrderEntity } from '@/core.constants';
import { UserType } from '@/core.constants';
import { firebaseTimestampToDate } from '@/utils/date-utils';
import { getImagePathWithFallback } from '@/utils/image-path';

import { activityTableMessages } from './intl/activity-table.messages';

interface ActivityTableProps {
  orders: OrderEntity[];
  loading: boolean;
  loadingMore: boolean;
  collections: CollectionEntity[];
}

export function ActivityTable({
  orders,
  loading,
  loadingMore,
  collections,
}: ActivityTableProps) {
  const { formatMessage: t } = useIntl();
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = () => {
    // Refresh orders if needed
  };

  const getCollection = (collectionId: string) => {
    return collections.find((c) => c.id === collectionId);
  };

  const getExecutionPrice = (order: OrderEntity) => {
    return order.secondaryMarketPrice || order.price;
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return firebaseTimestampToDate(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <Loader2 className="w-8 h-8 animate-spin text-[#708499]" />
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-[#708499] text-lg">
          {t(activityTableMessages.noActivityFound)}
        </p>
        <p className="text-[#708499] text-sm mt-2">
          {t(activityTableMessages.executedOrdersDescription)}
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-3">
        {orders.map((order) => {
          const collection = getCollection(order.collectionId);
          const executionPrice = getExecutionPrice(order);

          return (
            <div
              key={order.id}
              className="bg-[#232e3c] border border-[#3a4a5c] rounded-lg p-4 hover:bg-[#2a3441] transition-colors"
            >
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 rounded-lg overflow-hidden bg-[#17212b] flex-shrink-0">
                  {collection && (
                    <Image
                      src={getImagePathWithFallback(collection.id).primary}
                      alt={collection.name}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <h3 className="text-[#f5f5f5] font-medium">
                    {t(activityTableMessages.orderNumber, {
                      number: order.number || order.id?.slice(-6),
                    })}
                  </h3>
                </div>

                <div className="text-right flex-shrink-0">
                  <div className="text-[#f5f5f5] font-medium flex items-center gap-1 justify-end">
                    <span>{Math.floor(executionPrice * 100) / 100}</span>
                    <TonLogo size={16} />
                  </div>
                  <div className="text-[#708499] text-sm">
                    {formatDate(order.updatedAt)}
                  </div>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleOrderClick(order)}
                  className="flex-shrink-0 w-10 h-10 p-0"
                  aria-label={t(activityTableMessages.viewOrder)}
                >
                  <Eye className="w-4 h-4" />
                </Button>
              </div>
            </div>
          );
        })}

        {loadingMore && (
          <div className="flex justify-center py-4">
            <Loader2 className="w-6 h-6 animate-spin text-[#708499]" />
          </div>
        )}
      </div>

      <OrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userType={UserType.BUYER}
        onOrderAction={handleOrderAction}
        hideActionButton={true}
      />
    </>
  );
}
