'use client';

import { AlertTriangle, TrendingUp } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';

import { getAppConfig } from '@/api/app-config-api';
import { setSecondaryMarketPrice } from '@/api/order-api';
import { TonLogo } from '@/components/TonLogo';
import { BaseDrawer } from '@/components/ui/drawer/base-drawer';
import { DrawerActions } from '@/components/ui/drawer/drawer-actions';
import { DrawerHeader } from '@/components/ui/drawer/drawer-header';
import { Input } from '@/components/ui/input';
import type { OrderEntity } from '@/core.constants';
import { formatServerError } from '@/api/server-error-handler';
import { safeMultiply } from '@/utils/math-utils';

interface ResellOrderPriceDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  onOrderResold: () => void;
}

export function ResellOrderPriceDrawer({
  open,
  onOpenChange,
  order,
  onOrderResold,
}: ResellOrderPriceDrawerProps) {
  const { formatMessage: t } = useIntl();
  const [price, setPrice] = useState('');
  const [minPrice, setMinPrice] = useState(1);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadMinPrice = async () => {
      try {
        const config = await getAppConfig();
        if (config?.min_secondary_market_price) {
          setMinPrice(config.min_secondary_market_price);
        }
      } catch (error) {
        console.error('Error loading min price:', error);
      }
    };

    if (open) {
      loadMinPrice();
      setPrice('');
    }
  }, [open]);

  const priceValue = parseFloat(price);
  const buyerLockPercentageBPS = order?.fees?.buyer_locked_percentage ?? 0;
  const sellerLockPercentageBPS = order?.fees?.seller_locked_percentage ?? 0;
  const buyerLockPercentage = buyerLockPercentageBPS / 10000; // Convert BPS to decimal
  const sellerLockPercentage = sellerLockPercentageBPS / 10000; // Convert BPS to decimal

  const buyerLockedAmount = safeMultiply(
    order?.price ?? 0,
    buyerLockPercentage,
  );
  const sellerLockedAmount = safeMultiply(
    order?.price ?? 0,
    sellerLockPercentage,
  );
  const totalCollateral = buyerLockedAmount + sellerLockedAmount;

  const isValidPrice =
    !isNaN(priceValue) &&
    priceValue >= minPrice &&
    priceValue <= totalCollateral;
  const isPriceTooHigh = !isNaN(priceValue) && priceValue > totalCollateral;

  const handleSetPrice = async () => {
    if (!order?.id || !isValidPrice) return;

    setLoading(true);
    try {
      const result = await setSecondaryMarketPrice(order.id, priceValue);

      if (result.success) {
        toast.success('Secondary market order created successfully!');
        onOrderResold();
      } else {
        toast.error(
          result.message || 'Failed to create secondary market order',
        );
      }
    } catch (error) {
      console.error('Error setting secondary market price:', error);
      const errorMessage = formatServerError(error, t);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  if (!order) return null;

  return (
    <BaseDrawer
      open={open}
      onOpenChange={onOpenChange}
      zIndex={100}
      height="h-[60vh]"
      className="mt-32 z-[80]"
    >
      <DrawerHeader
        icon={TrendingUp}
        title="Set Resale Price"
        subtitle="Set your price for reselling this order on the secondary market"
      />

      <div className="space-y-6">
        <div className="bg-[#232e3c] rounded-xl p-4 border border-[#3a4a5c]">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-[#708499]">Original Price</span>
            <div className="flex items-center gap-1">
              <TonLogo size={24} />
              <span className="text-lg font-bold text-[#f5f5f5]">
                {order.price.toFixed(2)}
              </span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-[#708499]">
              Order #{order.number || order.id?.slice(-6)}
            </span>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label
              htmlFor="resale-price-input"
              className="block text-[#f5f5f5] font-medium mb-2"
            >
              Resale Price (TON)
            </label>
            <Input
              id="resale-price-input"
              type="number"
              step="0.01"
              min={minPrice}
              placeholder={`Minimum ${minPrice} TON`}
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              className="bg-[#232e3c] border-[#3a4a5c] text-[#f5f5f5] placeholder-[#708499] rounded-xl h-12"
            />
            <div className="flex justify-between items-center mt-2 text-sm">
              <span className="text-[#708499]">Minimum: {minPrice} TON</span>
              {priceValue > 0 && (
                <span
                  className={`font-medium ${isValidPrice ? 'text-green-400' : 'text-red-400'}`}
                >
                  {isValidPrice
                    ? '✓ Valid'
                    : isPriceTooHigh
                      ? '✗ Too high'
                      : '✗ Too low'}
                </span>
              )}
            </div>
            {totalCollateral > 0 && (
              <div className="mt-1 text-xs text-[#708499]">
                Maximum: {totalCollateral.toFixed(2)} TON (collateral sum)
              </div>
            )}
          </div>

          {isPriceTooHigh && (
            <div className="bg-red-900/20 rounded-xl p-4 border border-red-500/30">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-red-400">
                    Price Too High
                  </p>
                  <p className="text-xs text-red-300 leading-relaxed">
                    Resale price cannot exceed the sum of locked collaterals (
                    {totalCollateral.toFixed(2)} TON).
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="bg-[#2a3441] rounded-xl p-4 border border-[#3a4a5c]">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-[#f5f5f5]">
                  Important Notice
                </p>
                <p className="text-xs text-[#708499] leading-relaxed">
                  Once you set a resale price, your order will be listed on the
                  secondary market. Other users will be able to purchase it at
                  your set price.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <DrawerActions
        onPrimary={handleSetPrice}
        onCancel={handleCancel}
        primaryLabel="Set Resale Price"
        primaryLoadingLabel="Setting Price..."
        primaryDisabled={!isValidPrice}
        loading={loading}
        withConfirm
      />
    </BaseDrawer>
  );
}
