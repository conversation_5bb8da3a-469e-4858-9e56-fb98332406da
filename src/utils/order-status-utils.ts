import type { OrderEntity } from '@/core.constants';
import { OrderStatus, UserType } from '@/core.constants';

export interface StatusConfig {
  label: string;
  className: string;
  iconColor: string;
}

export const ORDER_STATUS_CONFIG: Record<OrderStatus, StatusConfig> = {
  [OrderStatus.ACTIVE]: {
    label: 'Active',
    className: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
    iconColor: 'text-blue-400',
  },
  [OrderStatus.PAID]: {
    label: 'Paid',
    className: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
    iconColor: 'text-yellow-400',
  },
  [OrderStatus.GIFT_SENT_TO_RELAYER]: {
    label: 'Sent to Bot',
    className: 'bg-purple-500/20 text-purple-400 border-purple-500/30',
    iconColor: 'text-purple-400',
  },
  [OrderStatus.FULFILLED]: {
    label: 'Fulfilled',
    className: 'bg-green-500/20 text-green-400 border-green-500/30',
    iconColor: 'text-green-400',
  },
  [OrderStatus.CANCELLED]: {
    label: 'Cancelled',
    className: 'bg-red-500/20 text-red-400 border-red-500/30',
    iconColor: 'text-red-400',
  },
};

export function getStatusConfig(status: OrderStatus): StatusConfig {
  return ORDER_STATUS_CONFIG[status];
}

export function getDeadlineTitle(
  order: OrderEntity,
  userType: UserType,
): string {
  if (order.status === OrderStatus.PAID) {
    return userType === UserType.SELLER
      ? 'Time to Send Gift'
      : 'Seller Deadline';
  }

  if (order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    return userType === UserType.BUYER
      ? 'Time to Claim Gift'
      : 'Buyer Deadline';
  }

  return 'Deadline';
}

export function getDeadlineDescription(
  order: OrderEntity,
  userType: UserType,
): string {
  if (order.status === OrderStatus.PAID) {
    return userType === UserType.SELLER
      ? 'Send gift to relayer or lose collateral'
      : 'Seller must send gift or lose collateral';
  }

  if (order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    return userType === UserType.BUYER
      ? 'Claim gift from relayer or lose collateral'
      : 'Buyer must claim gift or lose collateral';
  }

  return '';
}
