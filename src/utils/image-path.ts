import { CDN_BASE_URL } from '@/core.constants';

export const getImagePathWithFallback = (
  collectionId: string,
  format: 'png' | 'tgs' = 'png',
) => {
  if (!collectionId) return { primary: '', fallback: '' };

  const cdnUrl = `${CDN_BASE_URL}/${collectionId}/Original.${format}`;
  const localPath = `/limited/${collectionId}/Original.${format}`;

  return {
    primary: cdnUrl,
    fallback: localPath,
  };
};
